<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class ClientAuthController extends Controller
{
    /**
     * Show the client login form.
     */
    public function showLoginForm()
    {
        return view('client.auth.login');
    }

    /**
     * Handle client login.
     */
    public function login(Request $request)
    {
        $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);

    $credentials = $request->only('email', 'password');

    // Optional: Check if client exists and is active
    $client = Client::where('email', $credentials['email'])->first();
    if (!$client || $client->status !== '2') {
        return back()->withErrors([
            'email' => 'Account not active or does not exist.',
        ]);
    }

    if (Auth::guard('client')->attempt($credentials, $request->boolean('remember'))) {
        $request->session()->regenerate();
        return redirect()->route('client.dashboard');
    }

    return back()->withErrors([
        'email' => 'Invalid credentials.',
    ]);
    }

    /**
     * Handle client logout.
     */
    public function logout(Request $request)
    {
        Auth::guard('client')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('client.login');
    }
}
