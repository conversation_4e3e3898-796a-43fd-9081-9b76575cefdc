<?php

use App\Http\Controllers\Auth\ClientAuthController;
use App\Http\Controllers\ClientController;
use Illuminate\Support\Facades\Route;

// Guest routes (not authenticated)
Route::middleware('guest:client')->group(function () {
    Route::get('/login', [ClientAuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [ClientAuthController::class, 'login'])->name('makeLogin');
});

// Authenticated client routes
Route::middleware('auth:client')->group(function () {
    Route::get('/dashboard', [ClientController::class, 'dashboard'])->name('dashboard');
    Route::get('/sms-history', [ClientController::class, 'smsHistory'])->name('sms-history');
    Route::get('/profile', [ClientController::class, 'profile'])->name('profile');
    Route::get('/settings', [ClientController::class, 'settings'])->name('settings');
    Route::post('/logout', [ClientAuthController::class, 'logout'])->name('logout');
});
