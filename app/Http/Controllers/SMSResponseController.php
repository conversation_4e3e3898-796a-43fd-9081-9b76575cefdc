<?php

namespace App\Http\Controllers;

use App\Models\SMSResponse;
use Illuminate\Http\Request;

class SMSResponseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(SMSResponse $sMSResponse)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SMSResponse $sMSResponse)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SMSResponse $sMSResponse)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SMSResponse $sMSResponse)
    {
        //
    }
}
