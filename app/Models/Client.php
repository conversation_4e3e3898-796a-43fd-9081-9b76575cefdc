<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Client extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $guarded = [];

    protected $hidden = [
        'password',
        'authorization',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the SMS messages for the client.
     */
    public function sms(): HasMany
    {
        return $this->hasMany(SMS::class);
    }

    /**
     * Get the SMS responses for the client.
     */
    public function smsResponses(): HasMany
    {
        return $this->hasMany(SMSResponse::class);
    }
}
