<?php

namespace Database\Seeders;

use App\Models\Client;
use App\Models\SMS;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class ClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test clients
        $client1 = Client::create([
            'name' => 'Test Client 1',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'authorization' => 'auth_key_123',
            'status' => '2', // active
        ]);

        $client2 = Client::create([
            'name' => 'Test Client 2',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'authorization' => 'auth_key_456',
            'status' => '2', // active
        ]);

        // Create some sample SMS for client1
        SMS::create([
            'client_id' => $client1->id,
            'from_number' => '+1234567890',
            'to_number' => '+1987654321',
            'msg' => 'Hello! This is a test SMS message.',
            'charge' => 0.05,
            'status' => 'sent',
        ]);

        SMS::create([
            'client_id' => $client1->id,
            'from_number' => '+1234567890',
            'to_number' => '+1987654322',
            'msg' => 'Welcome to our service!',
            'charge' => 0.05,
            'status' => 'sent',
        ]);

        SMS::create([
            'client_id' => $client1->id,
            'from_number' => '+1234567890',
            'to_number' => '+1987654323',
            'msg' => 'Your verification code is 123456',
            'charge' => 0.05,
            'status' => 'failed',
        ]);

        SMS::create([
            'client_id' => $client1->id,
            'from_number' => '+1234567890',
            'to_number' => '+1987654324',
            'msg' => 'Thank you for using our service.',
            'charge' => 0.05,
            'status' => 'pending',
        ]);

        // Create some sample SMS for client2
        SMS::create([
            'client_id' => $client2->id,
            'from_number' => '+1234567891',
            'to_number' => '+1987654325',
            'msg' => 'Hi there! This is client 2 message.',
            'charge' => 0.07,
            'status' => 'sent',
        ]);
    }
}
