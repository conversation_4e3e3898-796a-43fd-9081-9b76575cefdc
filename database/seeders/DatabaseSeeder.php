<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('123123'),
        ]);

        $statuses = [['code' => '445000', 'msg' => 'msg Sent successfully'], ['code' => '445010', 'msg' => 'Missing API key'], ['code' => '445020', 'msg' => 'Missing contact number'], ['code' => '445030', 'msg' => 'Missing sender ID'], ['code' => '445040', 'msg' => 'Invalid API key'], ['code' => '445050', 'msg' => 'Your account was suspended'], ['code' => '445060', 'msg' => 'Your account was expired'], ['code' => '445070', 'msg' => 'Only a user can send SMS'], ['code' => '445080', 'msg' => 'Invalid sender ID'], ['code' => '445090', 'msg' => 'You have no access to this sender ID'], ['code' => '445110', 'msg' => 'All numbers are invalid'], ['code' => '445120', 'msg' => 'Insufficient balance'], ['code' => '445130', 'msg' => 'Reseller insufficient balance'], ['code' => '445170', 'msg' => 'You are not a user']];

        DB::table('s_m_s_responses')->insert($statuses);
    }
}
