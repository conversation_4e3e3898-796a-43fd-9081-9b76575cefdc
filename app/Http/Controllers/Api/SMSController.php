<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\SMS;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;


class SMSController extends Controller
{

public function send_sms(Request $request)
{
    try {
        $apiKey = $request->header('X-API-KEY');
        $clientInfo = Client::where('authorization', $apiKey)->first();

        if (!$apiKey || !$clientInfo) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $validator = Validator::make($request->all(), [
            'msg' => 'required|string',
            'contacts' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $smsApiKey = '4451551156210561551156210';
        $sender = '01844532630';

        $url = "http://sms.iglweb.com/api/v1/send";

        // Prepare payload
        $payload = [
            'api_key' => $smsApiKey,
            'contacts' => $request->contacts,
            'senderid' => $sender,
            'msg' => $request->msg,
        ];

        // Initialize cURL session
$ch = curl_init();

curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CUSTOMREQUEST => "GET",
    CURLOPT_HTTPHEADER => [
        "Content-Type: application/json",
    ],
    CURLOPT_POSTFIELDS => json_encode($payload),
    CURLOPT_TIMEOUT => 30,
    CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
    CURLOPT_SSL_VERIFYPEER => false, // Only for testing, remove in production
    CURLOPT_VERBOSE => true, // For detailed logging
]);

// Execute cURL request
$response = curl_exec($ch);

// Handle cURL error
if ($response === false) {
    $errorCode = curl_errno($ch);
    $errorMessage = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    curl_close($ch);

    return response()->json([
        'error' => 'SMS request failed',
        'curl_errno' => $errorCode,
        'curl_error' => $errorMessage,
        'http_code' => $httpCode,
        'details' => [
            'url' => $url,
            'payload' => $payload,
        ],
    ], 500);
}

// Get HTTP status code even if request didn't fail
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// Close cURL session
curl_close($ch);

// You might also want to check for HTTP errors
if ($httpCode >= 400) {
    return response()->json([
        'error' => 'SMS API returned error',
        'http_code' => $httpCode,
        'api_response' => $response,
    ], 500);
}

        // Decode JSON response
        $data = json_decode($response, true);

        // Insert SMS log
        SMS::insert([
            'client_id' => $clientInfo->id,
            'from_number' => $sender,
            'to_number' => $request->contacts,
            'msg' => $request->msg,
            'charge' => 0.84,
            'status' => $data['code'] ?? 'unknown',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return response()->json([
            'message' => $data['message'] ?? 'Message sent',
            'code' => $data['code'] ?? 200,
        ], 200);

    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Unexpected error',
            'message' => $e->getMessage(),
            'line' => $e->getLine(),
        ], 500);
    }
}




}
