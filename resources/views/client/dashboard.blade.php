<x-client-layout>
    <x-slot name="header">
        Dashboard
    </x-slot>

    <!-- Welcome Banner -->
    <div class="bg-gradient-to-r from-gray-900 via-black to-gray-900 rounded-2xl p-6 mb-6 text-white shadow-xl border border-gray-800">
        <div class="flex flex-col md:flex-row items-start md:items-center justify-between">
            <div class="mb-4 md:mb-0">
                <h2 class="text-2xl md:text-3xl font-bold mb-2">
                    Welcome back, {{ Auth::guard('client')->user()->name }}! 👋
                </h2>
                <p class="text-gray-300 text-lg">
                    Here's your SMS dashboard overview for today
                </p>
                <div class="flex items-center mt-3 text-gray-400">
                    <i class="fas fa-clock mr-2"></i>
                    <span class="text-sm">Last login: {{ Auth::guard('client')->user()->updated_at->diffForHumans() }}</span>
                </div>
            </div>
            <div class="text-right">
                <div class="bg-white bg-opacity-10 rounded-lg p-4 backdrop-blur-sm border border-gray-700">
                    <div class="text-2xl font-bold text-white">{{ now()->format('d M Y') }}</div>
                    <div class="text-gray-300 text-sm">{{ now()->format('l') }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6">
        <!-- Total SMS -->
        <div class="bg-white p-6 rounded-xl shadow-lg card-hover border-l-4 border-blue-500 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Total SMS</h3>
                    <p class="text-3xl font-bold text-gray-900 mt-2">{{ number_format($totalSms) }}</p>
                    @php
                        $successRate = $totalSms > 0 ? round(($smsStats['sent'] / $totalSms) * 100, 1) : 0;
                    @endphp
                    <p class="text-sm text-gray-500 mt-1 flex items-center font-medium">
                        <i class="fas fa-chart-line mr-1 text-blue-500"></i> {{ $successRate }}% success rate
                    </p>
                </div>
                <div class="bg-gradient-to-br from-blue-100 to-blue-200 p-4 rounded-full shadow-md">
                    <i class="fas fa-comment-sms text-blue-600 text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Charge -->
        <div class="bg-white p-6 rounded-xl shadow-lg card-hover border-l-4 border-green-500 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Total Spent</h3>
                    <p class="text-3xl font-bold text-gray-900 mt-2">৳{{ number_format($totalCharge, 2) }}</p>
                    @php
                        $avgCost = $totalSms > 0 ? round($totalCharge / $totalSms, 2) : 0;
                    @endphp
                    <p class="text-sm text-gray-500 mt-1 flex items-center font-medium">
                        <i class="fas fa-calculator mr-1 text-green-500"></i> ৳{{ $avgCost }} avg per SMS
                    </p>
                </div>
                <div class="bg-gradient-to-br from-green-100 to-green-200 p-4 rounded-full shadow-md">
                    <i class="fas fa-money-bill-wave text-green-600 text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- SMS Sent -->
        <div class="bg-white p-6 rounded-xl shadow-lg card-hover border-l-4 border-emerald-500 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">SMS Delivered</h3>
                    <p class="text-3xl font-bold text-gray-900 mt-2">{{ number_format($smsStats['sent']) }}</p>
                    <p class="text-sm text-gray-500 mt-1 flex items-center font-medium">
                        <i class="fas fa-check-circle mr-1 text-emerald-500"></i> Successfully delivered
                    </p>
                </div>
                <div class="bg-gradient-to-br from-emerald-100 to-emerald-200 p-4 rounded-full shadow-md">
                    <i class="fas fa-paper-plane text-emerald-600 text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- SMS Failed -->
        <div class="bg-white p-6 rounded-xl shadow-lg card-hover border-l-4 border-red-500 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">SMS Failed</h3>
                    <p class="text-3xl font-bold text-gray-900 mt-2">{{ number_format($smsStats['failed']) }}</p>
                    @php
                        $failureRate = $totalSms > 0 ? round(($smsStats['failed'] / $totalSms) * 100, 1) : 0;
                    @endphp
                    <p class="text-sm text-gray-500 mt-1 flex items-center font-medium">
                        <i class="fas fa-exclamation-triangle mr-1 text-red-500"></i> {{ $failureRate }}% failure rate
                    </p>
                </div>
                <div class="bg-gradient-to-br from-red-100 to-red-200 p-4 rounded-full shadow-md">
                    <i class="fas fa-times-circle text-red-600 text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Quick Stats Bar (visible only on mobile) -->
    <div class="lg:hidden bg-white rounded-xl shadow-lg p-4 mb-6 hover:shadow-xl transition-shadow duration-300">
        <div class="flex justify-between items-center">
            <div class="text-center">
                <div class="text-lg font-bold text-blue-600">{{ number_format($totalSms) }}</div>
                <div class="text-xs text-gray-600">Total SMS</div>
            </div>
            <div class="text-center">
                <div class="text-lg font-bold text-green-600">{{ $totalSms > 0 ? number_format(($smsStats['sent'] / $totalSms) * 100, 1) : 0 }}%</div>
                <div class="text-xs text-gray-600">Success</div>
            </div>
            <div class="text-center">
                <div class="text-lg font-bold text-purple-600">৳{{ number_format($totalCharge, 0) }}</div>
                <div class="text-xs text-gray-600">Spent</div>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Quick Actions -->
        <div class="bg-white p-6 rounded-xl shadow-lg card-hover hover:shadow-xl transition-all duration-300">
            <h3 class="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                Quick Actions
            </h3>
            <div class="grid grid-cols-1 gap-4">
                <a href="#" class="group flex items-center p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg hover:from-blue-100 hover:to-blue-200 transition-all duration-300 transform hover:scale-105 hover:shadow-md">
                    <div class="bg-blue-500 p-3 rounded-lg mr-4 group-hover:bg-blue-600 transition-colors shadow-sm">
                        <i class="fas fa-paper-plane text-white text-lg"></i>
                    </div>
                    <div class="flex-1">
                        <span class="font-semibold text-gray-800 group-hover:text-blue-700">Send SMS</span>
                        <p class="text-sm text-gray-600">Send instant messages</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-blue-500 transition-colors"></i>
                </a>

                <a href="{{ route('client.sms-history') }}" class="group flex items-center p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg hover:from-green-100 hover:to-green-200 transition-all duration-300 transform hover:scale-105 hover:shadow-md">
                    <div class="bg-green-500 p-3 rounded-lg mr-4 group-hover:bg-green-600 transition-colors shadow-sm">
                        <i class="fas fa-history text-white text-lg"></i>
                    </div>
                    <div class="flex-1">
                        <span class="font-semibold text-gray-800 group-hover:text-green-700">SMS History</span>
                        <p class="text-sm text-gray-600">View past messages</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-green-500 transition-colors"></i>
                </a>

                <a href="#" class="group flex items-center p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg hover:from-purple-100 hover:to-purple-200 transition-all duration-300 transform hover:scale-105 hover:shadow-md">
                    <div class="bg-purple-500 p-3 rounded-lg mr-4 group-hover:bg-purple-600 transition-colors shadow-sm">
                        <i class="fas fa-address-book text-white text-lg"></i>
                    </div>
                    <div class="flex-1">
                        <span class="font-semibold text-gray-800 group-hover:text-purple-700">Contacts</span>
                        <p class="text-sm text-gray-600">Manage contacts</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-purple-500 transition-colors"></i>
                </a>

                <a href="#" class="group flex items-center p-4 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg hover:from-orange-100 hover:to-orange-200 transition-all duration-300 transform hover:scale-105 hover:shadow-md">
                    <div class="bg-orange-500 p-3 rounded-lg mr-4 group-hover:bg-orange-600 transition-colors shadow-sm">
                        <i class="fas fa-credit-card text-white text-lg"></i>
                    </div>
                    <div class="flex-1">
                        <span class="font-semibold text-gray-800 group-hover:text-orange-700">Add Credits</span>
                        <p class="text-sm text-gray-600">Top up balance</p>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-orange-500 transition-colors"></i>
                </a>
            </div>
        </div>

        <!-- Account Balance -->
        <div class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 p-6 rounded-xl shadow-xl text-white relative overflow-hidden">
            <!-- Background decoration -->
            <div class="absolute top-0 right-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -mr-12 -mt-12"></div>
            <div class="absolute bottom-0 left-0 w-16 h-16 bg-white bg-opacity-10 rounded-full -ml-8 -mb-8"></div>
            
            <div class="relative z-10">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-wallet mr-2"></i>
                    Account Balance
                </h3>
                
                @php
                    // Calculate estimated balance based on spending
                    $estimatedBalance = max(1000 - $totalCharge, 0);
                    $estimatedCredits = max(floor($estimatedBalance / 0.25), 0); // Assuming 0.25 per SMS
                @endphp
                
                <div class="text-3xl font-bold mb-2">৳{{ number_format($estimatedBalance, 2) }}</div>
                <p class="text-blue-200 mb-4 flex items-center">
                    <i class="fas fa-coins mr-2"></i>
                    Available SMS Credits: {{ number_format($estimatedCredits) }}
                </p>
                
                <!-- Progress bar for balance -->
                @php
                    $balancePercentage = ($estimatedBalance / 1000) * 100;
                @endphp
                <div class="mb-4">
                    <div class="flex justify-between text-sm mb-1">
                        <span>Balance Usage</span>
                        <span>{{ number_format($balancePercentage, 1) }}%</span>
                    </div>
                    <div class="w-full bg-blue-800 rounded-full h-2">
                        <div class="bg-white h-2 rounded-full transition-all duration-500" style="width: {{ $balancePercentage }}%"></div>
                    </div>
                </div>
                
                <button class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-all duration-300 flex items-center justify-center w-full transform hover:scale-105 shadow-lg">
                    <i class="fas fa-plus mr-2"></i>Add Credits
                </button>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white p-6 rounded-xl shadow-lg card-hover hover:shadow-xl transition-all duration-300">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-clock text-green-600 mr-2"></i>
                Recent Activity
            </h3>
            <div class="space-y-4">
                @if($recentSms->count() > 0)
                    @foreach($recentSms->take(3) as $sms)
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                            @if($sms->status == 'sent' || $sms->status == '445000')
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-3 flex-shrink-0 shadow-sm"></div>
                            @elseif($sms->status == 'failed')
                                <div class="w-3 h-3 bg-red-500 rounded-full mr-3 flex-shrink-0 shadow-sm"></div>
                            @else
                                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3 flex-shrink-0 shadow-sm"></div>
                            @endif
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-800 truncate">
                                    SMS to {{ substr($sms->to_number, 0, 5) }}****
                                </p>
                                <p class="text-xs text-gray-500">{{ $sms->created_at->diffForHumans() }}</p>
                            </div>
                            <div class="text-xs font-semibold text-gray-700">
                                ৳{{ number_format($sms->charge, 2) }}
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-6">
                        <i class="fas fa-history text-4xl text-gray-300 mb-3"></i>
                        <p class="text-gray-800 font-medium">No recent activity</p>
                        <p class="text-sm text-gray-600">Your SMS activity will appear here</p>
                    </div>
                @endif

                @if($recentSms->count() > 3)
                    <div class="pt-3 border-t border-gray-200">
                        <a href="{{ route('client.sms-history') }}" class="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center justify-center transition-colors duration-200">
                            View all activity <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- SMS Statistics Chart -->
        <div class="bg-white p-6 rounded-xl shadow-lg card-hover hover:shadow-xl transition-all duration-300">
            <h3 class="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-chart-pie text-blue-600 mr-2"></i>
                SMS Statistics
            </h3>

            @if($totalSms > 0)
                <div class="relative">
                    <!-- Pie Chart Simulation -->
                    <div class="flex items-center justify-center mb-6">
                        <div class="relative w-32 h-32">
                            @php
                                $sentPercentage = ($smsStats['sent'] / $totalSms) * 100;
                                $failedPercentage = ($smsStats['failed'] / $totalSms) * 100;
                                $pendingPercentage = ($smsStats['pending'] / $totalSms) * 100;
                            @endphp

                            <!-- Background circle -->
                            <div class="w-32 h-32 rounded-full bg-gradient-to-r from-green-400 to-green-500 flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                {{ number_format($sentPercentage, 1) }}%
                            </div>
                        </div>
                    </div>

                    <!-- Legend -->
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-3 shadow-sm"></div>
                                <span class="font-medium text-gray-800">Delivered</span>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-gray-900">{{ number_format($smsStats['sent']) }}</div>
                                <div class="text-sm text-gray-600">{{ number_format($sentPercentage, 1) }}%</div>
                            </div>
                        </div>

                        @if($smsStats['failed'] > 0)
                            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg hover:bg-red-100 transition-colors duration-200">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-red-500 rounded-full mr-3 shadow-sm"></div>
                                    <span class="font-medium text-gray-800">Failed</span>
                                </div>
                                <div class="text-right">
                                    <div class="font-bold text-gray-900">{{ number_format($smsStats['failed']) }}</div>
                                    <div class="text-sm text-gray-600">{{ number_format($failedPercentage, 1) }}%</div>
                                </div>
                            </div>
                        @endif

                        @if($smsStats['pending'] > 0)
                            <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors duration-200">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3 shadow-sm"></div>
                                    <span class="font-medium text-gray-800">Pending</span>
                                </div>
                                <div class="text-right">
                                    <div class="font-bold text-gray-900">{{ number_format($smsStats['pending']) }}</div>
                                    <div class="text-sm text-gray-600">{{ number_format($pendingPercentage, 1) }}%</div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-chart-pie text-6xl text-gray-300 mb-4"></i>
                    <p class="text-gray-800 font-medium">No data to display</p>
                    <p class="text-sm text-gray-600">Send your first SMS to see statistics</p>
                </div>
            @endif
        </div>

        <!-- Performance Metrics -->
        <div class="bg-white p-6 rounded-xl shadow-lg card-hover hover:shadow-xl transition-all duration-300">
            <h3 class="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-tachometer-alt text-purple-600 mr-2"></i>
                Performance Metrics
            </h3>

            <div class="space-y-6">
                <!-- Delivery Rate -->
                <div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-700">Delivery Rate</span>
                        <span class="text-sm font-bold text-green-600">{{ $totalSms > 0 ? number_format(($smsStats['sent'] / $totalSms) * 100, 1) : 0 }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                        <div class="bg-gradient-to-r from-green-400 to-green-500 h-3 rounded-full transition-all duration-500 shadow-sm"
                             style="width: {{ $totalSms > 0 ? ($smsStats['sent'] / $totalSms) * 100 : 0 }}%"></div>
                    </div>
                </div>

                <!-- Average Cost -->
                <div class="p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-calculator text-blue-600 mr-3 text-lg"></i>
                            <span class="font-medium text-gray-800">Avg Cost/SMS</span>
                        </div>
                        <span class="text-lg font-bold text-blue-600">
                            ৳{{ $totalSms > 0 ? number_format($totalCharge / $totalSms, 2) : '0.00' }}
                        </span>
                    </div>
                </div>

                <!-- This Month Summary -->
                <div class="p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors duration-200">
                    <div class="flex items-center justify-between mb-2">
                        <span class="font-medium text-gray-800">This Month</span>
                        <span class="text-sm text-purple-600 font-medium">{{ now()->format('M Y') }}</span>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900">{{ number_format($totalSms) }}</div>
                            <div class="text-xs text-gray-600">Total SMS</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900">৳{{ number_format($totalCharge, 0) }}</div>
                            <div class="text-xs text-gray-600">Total Spent</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help & Tips Section -->
    <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 mb-8 border border-indigo-200 shadow-sm hover:shadow-md transition-shadow duration-300">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center shadow-sm">
                    <i class="fas fa-lightbulb text-indigo-600 text-lg"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Quick Tips</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-700">
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Keep phone numbers in international format</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Monitor your success rate regularly</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Check message history for delivery status</span>
                    </div>
                </div>
            </div>
            <div class="flex-shrink-0">
                <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1" onclick="this.parentElement.parentElement.parentElement.style.display='none'">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Recent SMS Table -->
    <div class="bg-white rounded-xl shadow-lg card-hover overflow-hidden hover:shadow-xl transition-all duration-300">
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-list text-blue-600 mr-2"></i>
                        Recent SMS Messages
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">Your latest SMS activity</p>
                </div>
                <a href="{{ route('client.sms-history') }}" class="mt-3 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 shadow-md">
                    <span class="font-medium text-sm">View All</span>
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
        
        @if($recentSms->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipient</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Charge</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($recentSms as $sms)
                            <tr class="hover:bg-blue-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center shadow-sm">
                                                <i class="fas fa-mobile-alt text-blue-600"></i>
                                            </div>
                                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-800">{{ $sms->to_number }}</div>
                            <div class="text-sm text-gray-500">Mobile Number</div>
                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 max-w-xs">
                                        <div class="truncate font-medium" title="{{ $sms->msg }}">
                                            {{ Str::limit($sms->msg, 50) }}
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            {{ strlen($sms->msg) }} characters
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($sms->status == 'sent' || $sms->status == '445000')
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 shadow-sm">
                                            <i class="fas fa-check mr-1"></i>
                                            Delivered
                                        </span>
                                    @elseif($sms->status == 'failed')
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 shadow-sm">
                                            <i class="fas fa-times mr-1"></i>
                                            Failed
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 shadow-sm">
                                            <i class="fas fa-clock mr-1"></i>
                                            Pending
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                    <div class="font-medium text-gray-800">{{ $sms->created_at->format('M d, Y') }}</div>
                                    <div class="text-xs text-gray-500">{{ $sms->created_at->format('h:i A') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="text-gray-900 font-bold">৳{{ number_format($sms->charge, 2) }}</div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="px-6 py-12 text-center">
                <div class="max-w-sm mx-auto">
                    <div class="bg-gray-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 shadow-sm">
                        <i class="fas fa-inbox text-3xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-800 mb-2">No SMS messages yet</h3>
                    <p class="text-gray-600 mb-6">Send your first SMS to get started and see your message history here.</p>
                    <a href="#" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 font-medium shadow-lg">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send First SMS
                    </a>
                </div>
            </div>
        @endif
    </div>

    <!-- Floating Action Button -->
    <div class="fixed bottom-6 right-6 z-50">
        <button class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110 group"
                onclick="window.location.href='#'"
                title="Send Quick SMS">
            <i class="fas fa-paper-plane text-xl group-hover:animate-pulse"></i>
        </button>
    </div>

    <!-- Auto-refresh indicator -->
    <div id="refresh-indicator" class="fixed top-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium opacity-0 transition-opacity duration-300 z-50">
        <i class="fas fa-sync-alt animate-spin mr-1"></i>
        Refreshing...
    </div>

</x-client-layout>

<script>
// Dashboard real-time updates
document.addEventListener('DOMContentLoaded', function() {
    // Add animation classes to cards
    const cards = document.querySelectorAll('.card-hover');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('slide-up');
        }, index * 100);
    });

    // Auto-refresh functionality
    let refreshInterval;
    
    function startAutoRefresh() {
        refreshInterval = setInterval(async () => {
            try {
                const indicator = document.getElementById('refresh-indicator');
                indicator.style.opacity = '1';
                
                // Here you would typically fetch new data from the API
                // For now, we'll just simulate the refresh
                setTimeout(() => {
                    indicator.style.opacity = '0';
                }, 1000);
                
            } catch (error) {
                console.error('Failed to refresh dashboard:', error);
            }
        }, 30000); // Refresh every 30 seconds
    }

    // Start auto-refresh
    startAutoRefresh();

    // Pause refresh when page is not visible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            clearInterval(refreshInterval);
        } else {
            startAutoRefresh();
        }
    });

    // Add smooth transitions to progress bars
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });

    // Add click to copy functionality for phone numbers
    document.querySelectorAll('.copy-phone').forEach(element => {
        element.addEventListener('click', function() {
            const phone = this.textContent;
            navigator.clipboard.writeText(phone).then(() => {
                // Show a brief success message
                const originalText = this.textContent;
                this.textContent = 'Copied!';
                this.classList.add('text-green-600');
                setTimeout(() => {
                    this.textContent = originalText;
                    this.classList.remove('text-green-600');
                }, 1000);
            });
        });
    });
});
</script>
