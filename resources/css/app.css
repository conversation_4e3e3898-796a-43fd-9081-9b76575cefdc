@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Dashboard Styles */
@layer components {
    .card-hover {
        @apply transition-all duration-300 ease-in-out;
    }

    .card-hover:hover {
        @apply transform -translate-y-1 shadow-xl;
    }

    .gradient-text {
        @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
    }

    .glass-effect {
        @apply backdrop-blur-sm bg-white bg-opacity-80 border border-white border-opacity-20;
    }

    .pulse-animation {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .slide-up {
        animation: slideUp 0.5s ease-out forwards;
    }

    .fade-in {
        animation: fadeIn 0.6s ease-out forwards;
    }

    .bounce-in {
        animation: bounceIn 0.8s ease-out forwards;
    }

    /* Enhanced card styling */
    .stats-card {
        @apply bg-white rounded-xl shadow-lg border-l-4 transform transition-all duration-300 hover:scale-105 hover:shadow-xl;
    }

    /* Better text contrast */
    .text-primary {
        @apply text-gray-800;
    }

    .text-secondary {
        @apply text-gray-600;
    }

    .text-muted {
        @apply text-gray-500;
    }

    /* Icon improvements */
    .icon-primary {
        @apply text-blue-600;
    }

    .icon-success {
        @apply text-green-500;
    }

    .icon-warning {
        @apply text-yellow-500;
    }

    .icon-danger {
        @apply text-red-500;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Loading skeleton animations */
.skeleton {
    animation: skeleton-loading 1s linear infinite alternate;
}

@keyframes skeleton-loading {
    0% {
        background-color: hsl(200, 20%, 80%);
    }
    100% {
        background-color: hsl(200, 20%, 95%);
    }
}

/* Notification styles */
.notification-enter {
    transform: translateX(100%);
    opacity: 0;
}

.notification-enter-active {
    transform: translateX(0);
    opacity: 1;
    transition: all 0.3s ease-out;
}

/* Modern button styles */
.btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

.btn-secondary {
    @apply bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

.btn-success {
    @apply bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

/* Stats card animations */
.stats-card {
    @apply transform transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

/* Progress bar animations */
.progress-bar {
    animation: progressFill 1.5s ease-out forwards;
}

@keyframes progressFill {
    from {
        width: 0%;
    }
}

/* Responsive grid improvements */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}
