<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SMS extends Model
{
    use HasFactory;

    protected $table = 's_m_s';
    protected $guarded = [];

    /**
     * Get the client that owns the SMS.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
}
