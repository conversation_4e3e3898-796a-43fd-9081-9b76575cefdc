<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\SMS;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ClientController extends Controller
{
    /**
     * Display the client dashboard.
     */
    public function dashboard()
    {
        $client = Auth::guard('client')->user();

        // Use caching for better performance
        $cacheKey = "client_dashboard_{$client->id}";
        
        $dashboardData = cache()->remember($cacheKey, now()->addMinutes(5), function() use ($client) {
            $totalSms = $client->sms()->count();
            $totalCharge = $client->sms()->sum('charge');
            
            // Improved SMS statistics with proper status handling
            $smsStats = [
                'total' => $totalSms,
                'sent' => $client->sms()->whereIn('status', ['sent', '445000'])->count(),
                'failed' => $client->sms()->where('status', 'failed')->count(),
                'pending' => $client->sms()->where('status', 'pending')->count(),
            ];

            // Add additional statistics for better dashboard insights
            $additionalStats = [
                'today_sms' => $client->sms()->whereDate('created_at', today())->count(),
                'today_charge' => $client->sms()->whereDate('created_at', today())->sum('charge'),
                'this_week_sms' => $client->sms()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                'this_month_sms' => $client->sms()->whereMonth('created_at', now()->month)->count(),
                'this_month_charge' => $client->sms()->whereMonth('created_at', now()->month)->sum('charge'),
                'avg_daily_sms' => $client->sms()->whereMonth('created_at', now()->month)->count() / max(now()->day, 1),
                'success_rate' => $totalSms > 0 ? round(($smsStats['sent'] / $totalSms) * 100, 1) : 0,
                'failure_rate' => $totalSms > 0 ? round(($smsStats['failed'] / $totalSms) * 100, 1) : 0,
                'avg_cost_per_sms' => $totalSms > 0 ? round($totalCharge / $totalSms, 2) : 0,
            ];

            return [
                'totalSms' => $totalSms,
                'totalCharge' => $totalCharge,
                'smsStats' => $smsStats,
                'additionalStats' => $additionalStats,
            ];
        });

        // Recent SMS should not be cached as it needs to be real-time
        $recentSms = $client->sms()->latest()->take(8)->get();

        return view('client.dashboard', array_merge($dashboardData, [
            'client' => $client,
            'recentSms' => $recentSms,
        ]));
    }

    /**
     * Display SMS history with data table.
     */
    public function smsHistory(Request $request)
    {
        $client = Auth::guard('client')->user();

        $query = $client->sms()->latest();

        // Filter by status if provided
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }

        // Filter by date range if provided
        if ($request->has('date_from') && $request->date_from != '') {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to != '') {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search in message content or phone numbers
        if ($request->has('search') && $request->search != '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('msg', 'like', "%{$search}%")
                  ->orWhere('to_number', 'like', "%{$search}%")
                  ->orWhere('from_number', 'like', "%{$search}%");
            });
        }

        $smsHistory = $query->paginate(20);

        return view('client.sms-history', compact('smsHistory'));
    }

    /**
     * Get dashboard statistics for API calls (for real-time updates)
     */
    public function getDashboardStats(Request $request)
    {
        $client = Auth::guard('client')->user();
        
        if (!$client) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $totalSms = $client->sms()->count();
        $totalCharge = $client->sms()->sum('charge');
        
        $smsStats = [
            'total' => $totalSms,
            'sent' => $client->sms()->whereIn('status', ['sent', '445000'])->count(),
            'failed' => $client->sms()->where('status', 'failed')->count(),
            'pending' => $client->sms()->where('status', 'pending')->count(),
        ];

        $additionalStats = [
            'today_sms' => $client->sms()->whereDate('created_at', today())->count(),
            'today_charge' => $client->sms()->whereDate('created_at', today())->sum('charge'),
            'success_rate' => $totalSms > 0 ? round(($smsStats['sent'] / $totalSms) * 100, 1) : 0,
            'failure_rate' => $totalSms > 0 ? round(($smsStats['failed'] / $totalSms) * 100, 1) : 0,
            'avg_cost_per_sms' => $totalSms > 0 ? round($totalCharge / $totalSms, 2) : 0,
        ];

        return response()->json([
            'totalSms' => $totalSms,
            'totalCharge' => $totalCharge,
            'smsStats' => $smsStats,
            'additionalStats' => $additionalStats,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Client $client)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Client $client)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Client $client)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Client $client)
    {
        //
    }

    /**
     * Display the client profile page.
     */
    public function profile()
    {
        $client = Auth::guard('client')->user();
        
        return view('client.profile', compact('client'));
    }

    /**
     * Display the client settings page.
     */
    public function settings()
    {
        $client = Auth::guard('client')->user();
        
        return view('client.settings', compact('client'));
    }
}
