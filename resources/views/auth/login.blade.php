<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Client Login - SMS City</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100 flex items-center justify-center p-4">
  <!-- Login Card -->
  <div class="w-full max-w-md bg-white rounded-xl shadow-xl p-8">

    <!-- Logo / Title -->
    <div class="text-center mb-6">
      <h2 class="text-3xl font-extrabold text-blue-600">Client Login</h2>
      <p class="text-gray-600 text-sm mt-2">Access your SMS dashboard</p>
    </div>

    <!-- Login Form -->
      <form method="POST" action="{{ route('client.makeLogin') }}">
        @csrf
      <!-- Email -->
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
        <input type="email" id="email" name="email" required
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500 px-3 py-2" />
      </div>

      <!-- Password -->
      <div>
        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
        <input type="password" id="password" name="password" required
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500 px-3 py-2" />
      </div>

      <!-- Remember Me + Admin Login Link -->
      <div class="flex items-center justify-between">
        <label class="flex items-center text-sm text-gray-700">
          <input type="checkbox" name="remember" class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
          Remember Me
        </label>
        <a href="/admin/login" class="text-sm text-blue-600 hover:underline">Admin Login</a>
      </div>

      <!-- Submit Button -->
      <button type="submit"
        class="w-full bg-blue-600 text-white py-2 rounded-md font-semibold hover:bg-blue-700 transition">
        Log in
      </button>
    </form>

    <!-- Footer -->
    <div class="mt-6 text-center text-sm text-gray-500">
      &copy; <span id="year"></span> SMS City. All rights reserved.
    </div>
  </div>

  <!-- Year Script -->
  <script>
    document.getElementById("year").textContent = new Date().getFullYear();
  </script>
</body>
</html>
