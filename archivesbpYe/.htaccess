<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTPS} !on

    RewriteRule ^.*$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>

# php -- B<PERSON>IN cPanel-generated handler, do not edit
# Set the “ea-php81” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php81 .php .php8 .phtml
</IfModule>
# php -- <PERSON><PERSON> cPanel-generated handler, do not edit
