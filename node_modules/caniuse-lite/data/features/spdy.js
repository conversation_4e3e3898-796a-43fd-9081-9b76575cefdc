module.exports={A:{A:{"1":"B","2":"K D E F A cC"},B:{"2":"2 3 4 5 6 7 8 9 C L M G N O P Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u AB BB CB DB EB FB GB v I"},C:{"1":"0 1 L M G N O P IB w x y z JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB","2":"2 3 4 5 6 7 8 9 dC EC J HB K D E F A B C iB jB kB lB mB nB oB pB FC qB GC rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B Q H R HC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u AB BB CB DB EB FB GB v I IC JC KC eC fC gC hC"},D:{"1":"0 1 J HB K D E F A B C L M G N O P IB w x y z JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB","2":"2 3 4 5 6 7 8 9 iB jB kB lB mB nB oB pB FC qB GC rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u AB BB CB DB EB FB GB v I IC JC KC"},E:{"1":"E F A B C mC MC 8B","2":"J HB K D iC LC jC kC lC","129":"L M G 9B nC oC pC NC OC AC qC BC PC QC RC SC TC rC CC UC VC WC XC YC ZC DC sC"},F:{"1":"0 1 G N O P IB w x y z JB KB LB MB NB OB PB QB RB SB TB UB VB WB ZB bB 9B","2":"F B C XB YB aB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B Q H R HC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u tC uC vC wC 8B aC xC"},G:{"1":"E 2C 3C 4C 5C 6C 7C 8C 9C","2":"LC yC bC zC 0C 1C","257":"AD BD CD DD ED FD GD HD NC OC AC ID BC PC QC RC SC TC JD CC UC VC WC XC YC ZC DC"},H:{"2":"KD"},I:{"1":"EC J OD bC PD QD","2":"I LD MD ND"},J:{"2":"D A"},K:{"1":"9B","2":"A B C H 8B aC"},L:{"2":"I"},M:{"2":"v"},N:{"1":"B","2":"A"},O:{"2":"AC"},P:{"1":"J","2":"0 1 w x y z RD SD TD UD VD MC WD XD YD ZD aD BC CC DC bD"},Q:{"2":"cD"},R:{"2":"dD"},S:{"1":"eD","2":"fD"}},B:7,C:"SPDY protocol",D:true};
